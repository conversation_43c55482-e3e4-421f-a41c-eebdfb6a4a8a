# ProManage Ribbon Button Logic

This document outlines the comprehensive ribbon button logic patterns used throughout the ProManage application, implemented via the centralized MenuRibbon User Control.

## Core Button States

### Edit Mode vs Ready Mode
- **Ready Mode** (`IsEditMode = false`): User can start operations (New/Edit/Delete enabled)
- **Edit Mode** (`IsEditMode = true`): User is actively editing (Save/Cancel enabled)

### Permission-Based Control
All buttons respect a two-level permission system:
1. **Global Permissions**: First-level filter (stored in users table)
2. **Form-Specific Permissions**: Second-level filter (role/user permissions)

## List Form Ribbon Button Logic

### Basic Operations
- **New Button**:
  - Enabled: `canCreate && !isEditMode`
  - Opens new entry form or creates new record

- **Edit Button**:
  - Enabled: `canEdit && !isEditMode && hasSelection`
  - Requires row selection via checkbox
  - Opens selected record for editing

- **Delete Button**:
  - Enabled: `canDelete && !isEditMode && hasSelection`
  - Requires row selection via checkbox
  - Shows confirmation dialog before deletion

- **Save Button**:
  - Enabled: `isEditMode && hasUnsavedChanges`
  - Only visible during edit operations

- **Cancel Button**:
  - Enabled: `isEditMode`
  - Discards unsaved changes and returns to ready mode

### Navigation Buttons
- **First/Previous/Next/Last**:
  - Enabled: `canRead && !isEditMode`
  - Disabled during edit mode to prevent data loss

### Print Operations
- **Print/Print Preview**:
  - Enabled: `canPrint`
  - Always available regardless of edit mode

### Grid Operations
- **Add Row**:
  - Enabled: `canEdit && isEditMode`
  - Only available during edit mode

## Entry Form Ribbon Button Logic

### Standard Entry Forms (UserMaster, RoleMaster, etc.)

#### Ready State (View Mode)
```
New: Enabled (if canCreate)
Edit: Enabled (if canEdit && hasData)
Delete: Enabled (if canDelete && hasData)
Save: Disabled
Cancel: Disabled
Navigation: Enabled (if canRead)
Print: Enabled (if canPrint)
```

#### New Mode
```
New: Disabled
Edit: Disabled
Delete: Disabled
Save: Enabled (if hasUnsavedChanges)
Cancel: Enabled
Navigation: Disabled
Print: Disabled
```

#### Edit Mode
```
New: Disabled
Edit: Disabled
Delete: Disabled
Save: Enabled (if hasUnsavedChanges)
Cancel: Enabled
Navigation: Disabled
Print: Disabled
```

### Special Form Configurations

#### Database Form
- All operation buttons hidden
- Only basic form functionality available

#### SQL Query Form
- Operation buttons hidden
- Print buttons available for query results
- Navigation buttons hidden

#### Permission Management Form
- Operation buttons visible
- Navigation buttons hidden (single-purpose form)
- Grid operations handled by form logic

## Button State Transition Logic

### State Transitions
1. **Ready → New Mode**:
   - New button clicked
   - Clear form, enable fields
   - Show Save/Cancel, hide others

2. **Ready → Edit Mode**:
   - Edit button clicked
   - Load data, enable fields
   - Show Save/Cancel, hide others

3. **Edit/New → Ready**:
   - Save or Cancel clicked
   - Disable fields, refresh data
   - Show New/Edit/Delete, hide Save/Cancel

### Validation Rules
- Save button only enabled when form has unsaved changes
- Navigation disabled during edit to prevent data loss
- Delete requires confirmation dialog
- Edit/Delete require data to be loaded

## Permission Integration

### Global Permission Checks
```csharp
bool canCreate = HasEffectivePermission("new");
bool canEdit = HasEffectivePermission("edit");
bool canDelete = HasEffectivePermission("delete");
bool canPrint = HasEffectivePermission("print");
bool canRead = HasEffectivePermission("read");
```

### Effective Permission Logic
1. Check global permission first
2. If global denied, deny access
3. If global allowed, check form-specific permission
4. Operational forms bypass permission checks

### Operational Forms
Forms that bypass strict permission control:
- Utility forms (PrintPreviewForm)
- Informational dialogs (AboutBox)
- Parameter entry utilities
- Non-business critical forms

## Implementation Pattern

### MenuRibbon UC Integration
```csharp
// Configure for form type
menuRibbon.ConfigureForFormType("usermaster");
menuRibbon.FormName = "UserMasterForm";
menuRibbon.CurrentUserId = currentUserId;

// Update states
menuRibbon.IsEditMode = isInEditMode;
menuRibbon.HasUnsavedChanges = isDirty;
menuRibbon.RefreshAll();
```

### Event Handling
```csharp
// Wire up events
menuRibbon.NewClicked += btnNew_ItemClick;
menuRibbon.EditClicked += btnEdit_ItemClick;
menuRibbon.SaveClicked += btnSave_ItemClick;
menuRibbon.CancelClicked += btnCancel_ItemClick;
menuRibbon.DeleteClicked += btnDelete_ItemClick;
```

## Error Handling

### Safety Measures
- All buttons disabled on permission errors
- Default to deny access on exceptions
- Design mode bypasses all permission checks
- Invalid user IDs result in disabled buttons

### Fallback Behavior
- Unknown form types use default configuration
- Missing permissions default to false
- Error states disable all operations for safety